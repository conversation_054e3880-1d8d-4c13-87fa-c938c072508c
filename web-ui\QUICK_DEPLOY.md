# Quick Deployment Guide

## 🚀 One-Click Deployment

### For Windows Users:
```powershell
.\deploy.ps1
```

### For Linux/macOS Users:
```bash
./deploy.sh
```

### Manual Deployment:
```bash
# 1. Create .env file (if not exists)
cp .env.example .env  # or create manually

# 2. Deploy with Docker
docker compose up --build -d

# 3. Access the application
# Web UI: http://localhost:7788
# VNC: http://localhost:6080/vnc.html
```

## �� What You Get

- **Simplified Web UI**: Clean interface with hidden agent settings
- **Automatic Configuration**: Uses Google Gemini by default with optimized settings
- **VNC Viewer**: Watch browser interactions in real-time
- **Multiple LLM Support**: OpenAI, Google, Anthropic, DeepSeek, Ollama, etc.
- **Custom Browser Support**: Use your own browser with persistent sessions
- **High-definition Recording**: Capture browser interactions

## 🔧 Configuration

### Basic Setup (Recommended)
Just add your API key to the `.env` file:

```env
# For Gemini (default)
GOOGLE_API_KEY=your_gemini_api_key_here

# For OpenAI
OPENAI_API_KEY=your_openai_api_key_here

# For Claude
ANTHROPIC_API_KEY=your_claude_api_key_here
```

### Advanced Configuration
The Agent Settings tab is hidden but fully configurable via environment variables. See `HIDDEN_AGENT_SETTINGS.md` for complete customization options.

## 🌐 Access Points

- **Main Interface**: http://localhost:7788
- **VNC Viewer**: http://localhost:6080/vnc.html
- **VNC Password**: `youvncpassword` (change in .env)

## 📚 Next Steps

1. **Add API Key**: Edit `.env` file with your LLM provider key
2. **Start Testing**: Go directly to "🤖 Run Agent" tab
3. **Customize Settings**: See `HIDDEN_AGENT_SETTINGS.md` for advanced options
4. **Production Setup**: See `DEPLOYMENT.md` for advanced configuration

## 🆘 Troubleshooting

```bash
# View logs
docker compose logs -f

# Restart services
docker compose restart

# Full reset
docker compose down -v
docker compose up --build -d
```

## 📖 Documentation

- **Hidden Agent Settings**: `HIDDEN_AGENT_SETTINGS.md`
- **Full Deployment Guide**: `DEPLOYMENT.md`
- **Main Documentation**: `README.md`
- **Security**: `SECURITY.md` 