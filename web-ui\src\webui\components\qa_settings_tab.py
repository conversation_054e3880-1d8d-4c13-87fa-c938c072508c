"""
QA Testing Settings Tab
Provides specialized settings for QA testing mode
"""

import gradio as gr
import os
from src.utils.qa_system_prompt import get_qa_extend_prompt, get_qa_enhanced_system_prompt

def create_qa_settings_tab():
    """Create QA testing settings interface"""
    
    with gr.Tab("QA Testing Settings"):
        gr.Markdown("## Quality Assurance Testing Configuration")
        gr.Markdown("Configure specialized settings for QA testing mode")
        
        with gr.<PERSON>():
            qa_mode_enabled = gr.Checkbox(
                label="Enable QA Testing Mode",
                value=os.getenv("DEFAULT_QA_MODE", "true").lower() == "true",
                info="Automatically inject QA testing behavior into the agent"
            )
            
            qa_severity_level = gr.Dropdown(
                label="Bug Severity Reporting",
                choices=["All Issues", "Major & Critical Only", "Critical Only"],
                value="All Issues",
                info="Filter which severity levels to report"
            )
        
        with gr.<PERSON>():
            qa_test_depth = gr.<PERSON>lider(
                label="Testing Depth",
                minimum=1,
                maximum=5,
                value=3,
                step=1,
                info="1=Basic, 3=Standard, 5=Exhaustive testing"
            )
            
            qa_edge_case_testing = gr.Checkbox(
                label="Enable Edge Case Testing",
                value=True,
                info="Test boundary values, invalid inputs, and unusual scenarios"
            )
        
        with gr.Accordion("QA System Prompts", open=False):
            with gr.Row():
                qa_prompt_type = gr.Radio(
                    label="QA Prompt Type",
                    choices=["Extend Default", "Override Complete", "Custom"],
                    value="Extend Default",
                    info="How to apply QA testing behavior"
                )
            
            qa_extend_prompt = gr.Textbox(
                label="QA Extend Prompt",
                lines=5,
                value=get_qa_extend_prompt(),
                info="Extends browser automation with QA behavior"
            )
            
            qa_override_prompt = gr.Textbox(
                label="QA Override Prompt (Advanced)",
                lines=10,
                value=get_qa_enhanced_system_prompt(),
                info="Complete system prompt replacement (use with caution)"
            )
        
        with gr.Accordion("QA Reporting Settings", open=False):
            qa_report_format = gr.Radio(
                label="Report Format",
                choices=["Structured", "Detailed", "Summary"],
                value="Structured",
                info="Format for QA test reports"
            )
            
            qa_include_screenshots = gr.Checkbox(
                label="Include Screenshots in Reports",
                value=True,
                info="Capture screenshots of issues found"
            )
            
            qa_auto_save_reports = gr.Checkbox(
                label="Auto-save QA Reports",
                value=True,
                info="Automatically save test reports to files"
            )
        
        # Preview section
        with gr.Accordion("QA Prompt Preview", open=False):
            qa_prompt_preview = gr.Textbox(
                label="Current QA Prompt",
                lines=15,
                interactive=False,
                value=get_qa_extend_prompt()
            )
            
            def update_prompt_preview(prompt_type, extend_prompt, override_prompt):
                if prompt_type == "Extend Default":
                    return extend_prompt
                elif prompt_type == "Override Complete":
                    return override_prompt
                else:
                    return "Custom prompt configuration"
            
            qa_prompt_type.change(
                fn=update_prompt_preview,
                inputs=[qa_prompt_type, qa_extend_prompt, qa_override_prompt],
                outputs=qa_prompt_preview
            )
        
        # Return components for external access
        return {
            "qa_mode_enabled": qa_mode_enabled,
            "qa_severity_level": qa_severity_level,
            "qa_test_depth": qa_test_depth,
            "qa_edge_case_testing": qa_edge_case_testing,
            "qa_prompt_type": qa_prompt_type,
            "qa_extend_prompt": qa_extend_prompt,
            "qa_override_prompt": qa_override_prompt,
            "qa_report_format": qa_report_format,
            "qa_include_screenshots": qa_include_screenshots,
            "qa_auto_save_reports": qa_auto_save_reports
        }

def get_qa_settings_from_components(components):
    """Extract QA settings from Gradio components"""
    return {
        "enabled": components.get("qa_mode_enabled", True),
        "severity_level": components.get("qa_severity_level", "All Issues"),
        "test_depth": components.get("qa_test_depth", 3),
        "edge_case_testing": components.get("qa_edge_case_testing", True),
        "prompt_type": components.get("qa_prompt_type", "Extend Default"),
        "extend_prompt": components.get("qa_extend_prompt", get_qa_extend_prompt()),
        "override_prompt": components.get("qa_override_prompt", get_qa_enhanced_system_prompt()),
        "report_format": components.get("qa_report_format", "Structured"),
        "include_screenshots": components.get("qa_include_screenshots", True),
        "auto_save_reports": components.get("qa_auto_save_reports", True)
    }
