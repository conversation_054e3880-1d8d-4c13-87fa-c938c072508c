# Hidden Agent Settings Configuration

The Agent Settings tab has been hidden to simplify the UI, but all settings are still configurable through environment variables.

## 🎯 Default Configuration

The application now uses these default settings automatically:

- **LLM Provider**: Google (Gemini)
- **Model**: gemini-2.0-flash
- **Temperature**: 0.6
- **Vision**: Enabled
- **Max Steps**: 100
- **Max Actions**: 10
- **Max Input Tokens**: 128,000
- **Tool Calling Method**: auto

## 🔧 Customization via Environment Variables

You can customize any setting by adding these variables to your `.env` file:

### LLM Configuration
```env
DEFAULT_LLM=google                    # Provider: google, openai, anthropic, etc.
DEFAULT_LLM_MODEL=gemini-2.0-flash   # Model name
DEFAULT_LLM_TEMPERATURE=0.6          # 0.0 to 2.0
DEFAULT_USE_VISION=true              # true/false
```

### Agent Configuration
```env
DEFAULT_MAX_STEPS=100                # Maximum steps per task
DEFAULT_MAX_ACTIONS=10               # Maximum actions per step
DEFAULT_MAX_INPUT_TOKENS=128000      # Maximum input tokens
DEFAULT_TOOL_CALLING_METHOD=auto     # auto, function_calling, json_mode, raw, tools
```

### System Prompts
```env
DEFAULT_OVERRIDE_SYSTEM_PROMPT=      # Override default system prompt
DEFAULT_EXTEND_SYSTEM_PROMPT=        # Extend default system prompt
```

### Planner LLM (Optional)
```env
DEFAULT_PLANNER_LLM_PROVIDER=        # Planner provider (empty = no planner)
DEFAULT_PLANNER_LLM_MODEL=           # Planner model
DEFAULT_PLANNER_LLM_TEMPERATURE=0.6  # Planner temperature
DEFAULT_PLANNER_USE_VISION=false     # Planner vision
```

### API Configuration
```env
DEFAULT_LLM_BASE_URL=                # Custom API endpoint
DEFAULT_LLM_API_KEY=                 # API key (usually left empty to use .env)
```

## 🚀 Quick Setup Examples

### For Gemini (Default)
```env
DEFAULT_LLM=google
DEFAULT_LLM_MODEL=gemini-2.0-flash
GOOGLE_API_KEY=your_gemini_api_key_here
```

### For OpenAI
```env
DEFAULT_LLM=openai
DEFAULT_LLM_MODEL=gpt-4o
OPENAI_API_KEY=your_openai_api_key_here
```

### For Claude
```env
DEFAULT_LLM=anthropic
DEFAULT_LLM_MODEL=claude-3-5-sonnet-20241022
ANTHROPIC_API_KEY=your_claude_api_key_here
```

### For Ollama (Local)
```env
DEFAULT_LLM=ollama
DEFAULT_LLM_MODEL=qwen2.5:7b
OLLAMA_ENDPOINT=http://localhost:11434
```

## 📋 Available Models

### Google (Gemini)
- gemini-2.0-flash
- gemini-2.0-flash-thinking-exp
- gemini-1.5-flash-latest
- gemini-2.5-pro-preview-03-25
- gemini-2.5-flash-preview-04-17

### OpenAI
- gpt-4o
- gpt-4
- gpt-3.5-turbo
- o3-mini

### Anthropic (Claude)
- claude-3-5-sonnet-20241022
- claude-3-5-sonnet-20240620
- claude-3-opus-20240229

### Ollama (Local)
- qwen2.5:7b
- qwen2.5:14b
- qwen2.5:32b
- llama2:7b
- deepseek-r1:14b

## 🔍 Verification

To verify your settings are working:

1. Check the application logs for any configuration errors
2. Try running a simple task in the "🤖 Run Agent" tab
3. The agent should use your configured settings automatically

## 🆘 Troubleshooting

If the agent isn't using your settings:

1. **Check environment variables**: Ensure they're properly set in `.env`
2. **Restart the application**: Changes require a restart
3. **Check logs**: Look for configuration errors
4. **Verify API keys**: Ensure your API keys are valid

## 📝 Notes

- The Agent Settings tab is hidden but the functionality remains
- All settings are applied automatically on startup
- Changes to environment variables require application restart
- API keys should be set in the main `.env` file (e.g., `GOOGLE_API_KEY`)
- The application will fall back to OpenAI if the configured provider is not available 