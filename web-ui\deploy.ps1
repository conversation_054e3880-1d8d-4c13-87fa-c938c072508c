# Browser-Use Web UI Deployment Script for Windows
# This script automates the deployment process

param(
    [switch]$SkipEnvCheck
)

Write-Host "🚀 Starting Browser-Use Web UI Deployment..." -ForegroundColor Green

# Check if Docker is installed
try {
    docker --version | Out-Null
    Write-Host "✅ Docker is installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not installed. Please install Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Check if Docker Compose is installed
try {
    docker compose version | Out-Null
    Write-Host "✅ Docker Compose is installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose is not installed. Please install Docker Compose first." -ForegroundColor Red
    exit 1
}

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "⚠️  .env file not found. Creating from template..." -ForegroundColor Yellow
    
    # Create .env file with default values
    @"
# LLM API Keys & Endpoints
OPENAI_ENDPOINT=https://api.openai.com/v1
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_ENDPOINT=https://api.anthropic.com
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_API_VERSION=2025-01-01-preview
DEEPSEEK_ENDPOINT=https://api.deepseek.com
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OLLAMA_ENDPOINT=http://localhost:11434
MISTRAL_ENDPOINT=https://api.mistral.ai/v1
MISTRAL_API_KEY=your_mistral_api_key_here
ALIBABA_ENDPOINT=https://dashscope.aliyuncs.com/compatible-mode/v1
ALIBABA_API_KEY=your_alibaba_api_key_here
MOONSHOT_ENDPOINT=https://api.moonshot.cn/v1
MOONSHOT_API_KEY=your_moonshot_api_key_here
UNBOUND_ENDPOINT=https://api.getunbound.ai
UNBOUND_API_KEY=your_unbound_api_key_here
SiliconFLOW_ENDPOINT=https://api.siliconflow.cn/v1/
SiliconFLOW_API_KEY=your_siliconflow_api_key_here
IBM_ENDPOINT=https://us-south.ml.cloud.ibm.com
IBM_API_KEY=your_ibm_api_key_here
IBM_PROJECT_ID=your_ibm_project_id_here

# Application Settings
ANONYMIZED_TELEMETRY=false
BROWSER_USE_LOGGING_LEVEL=info
DEFAULT_LLM=google
DEFAULT_LLM_MODEL=gemini-2.0-flash
DEFAULT_LLM_TEMPERATURE=0.6
DEFAULT_USE_VISION=true
DEFAULT_MAX_STEPS=100
DEFAULT_MAX_ACTIONS=10
DEFAULT_MAX_INPUT_TOKENS=128000
DEFAULT_TOOL_CALLING_METHOD=auto

# Browser Settings
BROWSER_PATH=
BROWSER_USER_DATA=
BROWSER_DEBUGGING_PORT=9222
BROWSER_DEBUGGING_HOST=localhost
USE_OWN_BROWSER=false
KEEP_BROWSER_OPEN=true
BROWSER_CDP=

# Display Settings
DISPLAY=:99
PLAYWRIGHT_BROWSERS_PATH=/ms-browsers
RESOLUTION=1920x1080x24
RESOLUTION_WIDTH=1920
RESOLUTION_HEIGHT=1080

# VNC Settings
VNC_PASSWORD=youvncpassword
"@ | Out-File -FilePath ".env" -Encoding UTF8
    
    Write-Host "✅ .env file created. Please edit it to add your API keys." -ForegroundColor Green
    Write-Host "📝 You can edit the .env file now or continue with default settings." -ForegroundColor Yellow
    
    if (-not $SkipEnvCheck) {
        $response = Read-Host "Continue with deployment? (y/n)"
        if ($response -ne "y" -and $response -ne "Y") {
            Write-Host "Deployment cancelled." -ForegroundColor Yellow
            exit 0
        }
    }
}

# Detect system architecture
$arch = (Get-WmiObject -Class Win32_Processor).Architecture
if ($arch -eq 12) {  # ARM64
    Write-Host "🖥️  Detected ARM64 architecture. Using ARM64 build..." -ForegroundColor Yellow
    $env:TARGETPLATFORM = "linux/arm64"
}

# Stop existing containers if running
Write-Host "🛑 Stopping existing containers..." -ForegroundColor Yellow
try {
    docker compose down 2>$null
} catch {
    # Ignore errors if no containers are running
}

# Build and start the application
Write-Host "🔨 Building and starting the application..." -ForegroundColor Yellow
docker compose up --build -d

# Wait for services to start
Write-Host "⏳ Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check if services are running
$services = docker compose ps --format json | ConvertFrom-Json
$runningServices = $services | Where-Object { $_.State -eq "running" }

if ($runningServices) {
    Write-Host "✅ Deployment successful!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Access your application:" -ForegroundColor Cyan
    Write-Host "   Web UI: http://localhost:7788" -ForegroundColor White
    Write-Host "   VNC Viewer: http://localhost:6080/vnc.html" -ForegroundColor White
    Write-Host "   VNC Password: youvncpassword (change in .env file)" -ForegroundColor White
    Write-Host ""
    Write-Host "📋 Useful commands:" -ForegroundColor Cyan
    Write-Host "   View logs: docker compose logs -f" -ForegroundColor White
    Write-Host "   Stop: docker compose down" -ForegroundColor White
    Write-Host "   Restart: docker compose restart" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 To customize:" -ForegroundColor Cyan
    Write-Host "   - Edit .env file for API keys and settings" -ForegroundColor White
    Write-Host "   - Modify docker-compose.yml for custom ports" -ForegroundColor White
    Write-Host "   - Check DEPLOYMENT.md for advanced configuration" -ForegroundColor White
} else {
    Write-Host "❌ Deployment failed. Check logs with: docker compose logs" -ForegroundColor Red
    exit 1
} 