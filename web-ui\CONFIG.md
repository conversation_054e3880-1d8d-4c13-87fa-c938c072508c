# Configuration Guide for DrCode Web UI

With the UI settings components hidden, you can configure the default LLM (Large Language Model) and other agent settings using environment variables or by editing configuration files in the codebase.

## 1. Changing the Default LLM Provider

The default LLM provider (e.g., OpenAI, Google Gemini, Anthropic Claude, etc.) is controlled by the `DEFAULT_LLM` environment variable.

**To change the default LLM:**

- Open your `.env` file in the `web-ui` directory.
- Set the `DEFAULT_LLM` variable to your desired provider. For example:

```env
DEFAULT_LLM=google           # For Google Gemini
DEFAULT_LLM=openai           # For OpenAI GPT
DEFAULT_LLM=anthropic        # For Claude
```

**Supported values:**
- `openai`
- `google`
- `anthropic`
- `deepseek`
- `ollama`
- ... (see `src/utils/config.py` for the full list)

## 2. Changing the Default Model and Other Settings

You can also set the default model, temperature, and other agent parameters in your `.env` file:

```env
DEFAULT_LLM_MODEL=gemini-2.0-flash      # Model name for the provider
DEFAULT_LLM_TEMPERATURE=0.6             # LLM temperature (0.0 - 2.0)
DEFAULT_USE_VISION=true                 # Enable vision input (true/false)
DEFAULT_MAX_STEPS=100                   # Max steps per agent run
DEFAULT_MAX_ACTIONS=10                  # Max actions per step
DEFAULT_MAX_INPUT_TOKENS=128000         # Max input tokens
DEFAULT_TOOL_CALLING_METHOD=auto        # Tool calling method
```

**Example for OpenAI:**
```env
DEFAULT_LLM=openai
DEFAULT_LLM_MODEL=gpt-4o
OPENAI_API_KEY=your_openai_api_key_here
```

**Example for Google Gemini:**
```env
DEFAULT_LLM=google
DEFAULT_LLM_MODEL=gemini-2.0-flash
GOOGLE_API_KEY=your_gemini_api_key_here
```

## 3. API Keys

Set your API keys in the `.env` file as well:

```env
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_gemini_api_key_here
ANTHROPIC_API_KEY=your_claude_api_key_here
```

## 4. Advanced: Editing Defaults in Code

If you want to hard-code defaults, you can edit `src/utils/default_settings.py`:

```python
DEFAULT_LLM_PROVIDER = os.getenv("DEFAULT_LLM", "google")
DEFAULT_LLM_MODEL = os.getenv("DEFAULT_LLM_MODEL", "gemini-2.0-flash")
DEFAULT_LLM_TEMPERATURE = float(os.getenv("DEFAULT_LLM_TEMPERATURE", "0.6"))
# ...
```

## 5. Full List of Supported Models

See `src/utils/config.py` for all supported providers and model names.

## 6. Applying Changes

- **After editing `.env` or code files, restart the web UI** for changes to take effect.
- If using Docker, rebuild/restart your container:
  ```bash
  docker compose down
  docker compose up --build -d
  ```

## 7. Troubleshooting

- If the agent does not use your new settings, double-check your `.env` file and restart the app.
- Check logs for any configuration errors.

---

**Summary:**
- All agent and LLM settings are now controlled via environment variables or config files.
- Edit `.env` for most changes, or `src/utils/default_settings.py` for hard-coded defaults.
- Restart the app after making changes. 