# Deployment Guide for Browser-Use Web UI

This guide will help you deploy the Browser-Use Web UI application using Docker.

## Prerequisites

- Docker and Docker Compose installed
  - [Docker Desktop](https://www.docker.com/products/docker-desktop/) (For Windows/macOS)
  - [Docker Engine](https://docs.docker.com/engine/install/) and [Docker Compose](https://docs.docker.com/compose/install/) (For Linux)

## Quick Deployment

### Step 1: Clone and Navigate
```bash
git clone <your-repo-url>
cd web-ui
```

### Step 2: Create Environment File
Create a `.env` file in the web-ui directory with your configuration:

```bash
# Copy the example (if available)
cp .env.example .env

# Or create manually
touch .env
```

Add your configuration to the `.env` file:

```env
# LLM API Keys & Endpoints
OPENAI_ENDPOINT=https://api.openai.com/v1
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_ENDPOINT=https://api.anthropic.com
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_API_VERSION=2025-01-01-preview
DEEPSEEK_ENDPOINT=https://api.deepseek.com
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OLLAMA_ENDPOINT=http://localhost:11434
MISTRAL_ENDPOINT=https://api.mistral.ai/v1
MISTRAL_API_KEY=your_mistral_api_key_here
ALIBABA_ENDPOINT=https://dashscope.aliyuncs.com/compatible-mode/v1
ALIBABA_API_KEY=your_alibaba_api_key_here
MOONSHOT_ENDPOINT=https://api.moonshot.cn/v1
MOONSHOT_API_KEY=your_moonshot_api_key_here
UNBOUND_ENDPOINT=https://api.getunbound.ai
UNBOUND_API_KEY=your_unbound_api_key_here
SiliconFLOW_ENDPOINT=https://api.siliconflow.cn/v1/
SiliconFLOW_API_KEY=your_siliconflow_api_key_here
IBM_ENDPOINT=https://us-south.ml.cloud.ibm.com
IBM_API_KEY=your_ibm_api_key_here
IBM_PROJECT_ID=your_ibm_project_id_here

# Application Settings
ANONYMIZED_TELEMETRY=false
BROWSER_USE_LOGGING_LEVEL=info
DEFAULT_LLM=google
DEFAULT_LLM_MODEL=gemini-2.0-flash
DEFAULT_LLM_TEMPERATURE=0.6
DEFAULT_USE_VISION=true
DEFAULT_MAX_STEPS=100
DEFAULT_MAX_ACTIONS=10
DEFAULT_MAX_INPUT_TOKENS=128000
DEFAULT_TOOL_CALLING_METHOD=auto

# Browser Settings
BROWSER_PATH=
BROWSER_USER_DATA=
BROWSER_DEBUGGING_PORT=9222
BROWSER_DEBUGGING_HOST=localhost
USE_OWN_BROWSER=false
KEEP_BROWSER_OPEN=true
BROWSER_CDP=

# Display Settings
DISPLAY=:99
PLAYWRIGHT_BROWSERS_PATH=/ms-browsers
RESOLUTION=1920x1080x24
RESOLUTION_WIDTH=1920
RESOLUTION_HEIGHT=1080

# VNC Settings
VNC_PASSWORD=youvncpassword
```

### Step 3: Deploy with Docker Compose

#### For x86_64 systems:
```bash
docker compose up --build -d
```

#### For ARM64 systems (e.g., Apple Silicon Macs):
```bash
TARGETPLATFORM=linux/arm64 docker compose up --build -d
```

### Step 4: Access the Application

- **Web UI**: Open `http://localhost:7788` in your browser
- **VNC Viewer** (for watching browser interactions): Open `http://localhost:6080/vnc.html`
  - Default VNC password: "youvncpassword" (can be changed in `.env`)

## Production Deployment

### 1. Environment Configuration

For production, ensure you have:
- All necessary API keys configured in `.env`
- Secure VNC password
- Proper firewall rules

### 2. Custom Ports (Optional)

To use different ports, modify the `docker-compose.yml`:

```yaml
ports:
  - "8080:7788"  # Web UI on port 8080
  - "6080:6080"  # VNC on port 6080
  - "5901:5901"  # Direct VNC on port 5901
  - "9222:9222"  # Browser debugging on port 9222
```

### 3. Persistent Browser Data (Optional)

To persist browser data between container restarts, uncomment and configure the volume in `docker-compose.yml`:

```yaml
volumes:
  - /tmp/.X11-unix:/tmp/.X11-unix
  - ./chrome_data:/app/data/chrome_data  # Uncomment this line
```

### 4. Reverse Proxy Setup (Recommended for Production)

For production, use a reverse proxy like Nginx:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:7788;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Docker Commands

### Basic Operations
```bash
# Start the application
docker compose up -d

# Stop the application
docker compose down

# View logs
docker compose logs -f

# Rebuild and restart
docker compose up --build -d

# Access container shell
docker compose exec browser-use-webui bash
```

### Troubleshooting
```bash
# Check container status
docker compose ps

# View detailed logs
docker compose logs browser-use-webui

# Restart specific service
docker compose restart browser-use-webui

# Clean up everything
docker compose down -v --remove-orphans
```

## Security Considerations

1. **API Keys**: Never commit `.env` files to version control
2. **VNC Password**: Change the default VNC password in production
3. **Network Security**: Use reverse proxy with SSL/TLS in production
4. **Container Security**: Regularly update base images and dependencies

## Monitoring

The application includes health checks and can be monitored via:
- Docker health checks
- Application logs
- VNC connection status

## Scaling

For horizontal scaling, consider:
- Load balancer for multiple instances
- Shared storage for browser data
- Database for session management (if needed)

## Support

For issues and questions:
- Check the application logs
- Review the main README.md
- Check the browser-use documentation 