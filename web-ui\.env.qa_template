# QA Testing Configuration Template
# Copy this to .env and modify as needed

# ===== QA TESTING MODE =====
# Enable automatic QA testing behavior
DEFAULT_QA_MODE=true

# ===== LLM CONFIGURATION =====
# Recommended models for QA testing
DEFAULT_LLM=google
DEFAULT_LLM_MODEL=gemini-2.0-flash
DEFAULT_LLM_TEMPERATURE=0.3
DEFAULT_USE_VISION=true

# ===== AGENT CONFIGURATION =====
# QA testing typically needs more steps and actions
DEFAULT_MAX_STEPS=150
DEFAULT_MAX_ACTIONS=15
DEFAULT_MAX_INPUT_TOKENS=128000
DEFAULT_TOOL_CALLING_METHOD=auto

# ===== QA SYSTEM PROMPTS =====
# Override the default system prompt completely (not recommended)
DEFAULT_OVERRIDE_SYSTEM_PROMPT=

# Extend the default system prompt with QA behavior (recommended)
# This will be auto-populated from sys_prompt.txt if available
DEFAULT_EXTEND_SYSTEM_PROMPT=

# ===== API KEYS =====
# Add your API keys here
GOOGLE_API_KEY=your_google_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# ===== BROWSER CONFIGURATION =====
# Browser settings for QA testing
BROWSER_PATH=
BROWSER_USER_DATA=

# ===== PLANNER LLM (Optional) =====
# Use a separate model for planning QA test strategies
DEFAULT_PLANNER_LLM_PROVIDER=google
DEFAULT_PLANNER_LLM_MODEL=gemini-2.0-flash
DEFAULT_PLANNER_LLM_TEMPERATURE=0.5
DEFAULT_PLANNER_USE_VISION=true

# ===== OLLAMA SETTINGS (if using Ollama) =====
DEFAULT_OLLAMA_NUM_CTX=32000
DEFAULT_PLANNER_OLLAMA_NUM_CTX=16000

# ===== CUSTOM API ENDPOINTS (if needed) =====
DEFAULT_LLM_BASE_URL=
DEFAULT_LLM_API_KEY=
DEFAULT_PLANNER_LLM_BASE_URL=
DEFAULT_PLANNER_LLM_API_KEY=
