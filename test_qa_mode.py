#!/usr/bin/env python3
"""
Test script to verify QA mode functionality
"""

import os
import sys
sys.path.append('web-ui/src')

from utils.qa_system_prompt import get_qa_extend_prompt, get_qa_enhanced_system_prompt
from utils.default_settings import get_default_agent_settings

def test_qa_prompt_loading():
    """Test if QA prompts are loaded correctly"""
    print("Testing QA Prompt Loading...")
    
    # Test extend prompt
    extend_prompt = get_qa_extend_prompt()
    print(f"✓ QA Extend Prompt loaded: {len(extend_prompt)} characters")
    print(f"Preview: {extend_prompt[:100]}...")
    
    # Test enhanced prompt
    enhanced_prompt = get_qa_enhanced_system_prompt()
    print(f"✓ QA Enhanced Prompt loaded: {len(enhanced_prompt)} characters")
    print(f"Preview: {enhanced_prompt[:100]}...")
    
    return True

def test_default_settings():
    """Test if default settings include QA configuration"""
    print("\nTesting Default Settings...")
    
    settings = get_default_agent_settings()
    
    # Check if QA-related settings are present
    qa_keys = ['override_system_prompt', 'extend_system_prompt']
    for key in qa_keys:
        if key in settings:
            print(f"✓ {key}: {len(str(settings[key]))} characters")
        else:
            print(f"✗ {key}: Missing")
    
    return True

def test_sys_prompt_loading():
    """Test if sys_prompt.txt is loaded correctly"""
    print("\nTesting sys_prompt.txt Loading...")
    
    sys_prompt_path = "sys_prompt.txt"
    if os.path.exists(sys_prompt_path):
        with open(sys_prompt_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        print(f"✓ sys_prompt.txt loaded: {len(content)} characters")
        print(f"Preview: {content[:100]}...")
        return True
    else:
        print("✗ sys_prompt.txt not found")
        return False

def test_environment_variables():
    """Test QA-related environment variables"""
    print("\nTesting Environment Variables...")
    
    qa_env_vars = [
        'DEFAULT_QA_MODE',
        'DEFAULT_OVERRIDE_SYSTEM_PROMPT',
        'DEFAULT_EXTEND_SYSTEM_PROMPT'
    ]
    
    for var in qa_env_vars:
        value = os.getenv(var, "Not set")
        print(f"  {var}: {value}")
    
    return True

def main():
    """Run all tests"""
    print("QA Mode Functionality Test")
    print("=" * 50)
    
    tests = [
        test_qa_prompt_loading,
        test_default_settings,
        test_sys_prompt_loading,
        test_environment_variables
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test failed: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print(f"Test Results: {sum(results)}/{len(results)} passed")
    
    if all(results):
        print("✓ All tests passed! QA mode should work correctly.")
    else:
        print("✗ Some tests failed. Check the configuration.")
    
    print("\nTo enable QA mode:")
    print("1. Set DEFAULT_QA_MODE=true in your .env file")
    print("2. Or use the QA Testing Settings tab in the web UI")
    print("3. Restart the web-ui application")

if __name__ == "__main__":
    main()
