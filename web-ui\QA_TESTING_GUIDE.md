# QA Testing Agent Setup Guide

This guide explains how to configure the web-ui to behave as a specialized QA testing agent instead of a general task-following agent.

## Quick Setup

### Method 1: Environment Variables (Recommended)
1. Copy `.env.qa_template` to `.env`
2. Set `DEFAULT_QA_MODE=true` in your `.env` file
3. Add your API keys to the `.env` file
4. Restart the web-ui

### Method 2: UI Configuration
1. Go to the "🧪 QA Testing Settings" tab
2. Enable "QA Testing Mode"
3. Configure testing depth and behavior
4. The agent will automatically use QA prompts

### Method 3: Manual Prompt Configuration
1. Go to the hidden "⚙️ Agent Settings" tab (if visible)
2. Use the "Extend System Prompt" field with your QA prompt
3. Or use "Override System Prompt" for complete control

## How It Works

### System Prompt Architecture
The web-ui uses a layered prompt system:

1. **Base Browser Automation Prompt**: Handles browser interaction, element detection, and tool usage
2. **QA Extension Prompt**: Adds QA testing behavior, critical thinking, and bug detection
3. **User Task**: Your specific testing assignment

### QA Behavior Injection
When QA mode is enabled, the agent automatically:
- Becomes more critical and thorough
- Tests edge cases and boundary conditions
- Looks for bugs, inconsistencies, and usability issues
- Provides structured QA reports
- Persists through blockers and adapts testing strategies

## Configuration Options

### QA Mode Levels
- **Basic (1)**: Simple functionality testing
- **Standard (3)**: Comprehensive testing with edge cases
- **Exhaustive (5)**: Deep testing with extensive edge case coverage

### Prompt Types
- **Extend Default**: Adds QA behavior to browser automation (recommended)
- **Override Complete**: Replaces entire system prompt (advanced users only)
- **Custom**: Use your own prompt configuration

### Environment Variables
```bash
# Enable QA mode
DEFAULT_QA_MODE=true

# Use your QA prompt from sys_prompt.txt
DEFAULT_EXTEND_SYSTEM_PROMPT=

# Or set a custom QA prompt directly
DEFAULT_EXTEND_SYSTEM_PROMPT="You are a QA tester. Be critical and thorough..."
```

## Usage Examples

### Example 1: Login Flow Testing
```
Task: "Test the login functionality at example.com"

QA Agent will:
- Test valid credentials
- Test invalid credentials
- Test empty fields
- Test SQL injection attempts
- Test password visibility toggle
- Test remember me functionality
- Test forgot password flow
- Document all issues found
```

### Example 2: Form Validation Testing
```
Task: "Test the contact form on the website"

QA Agent will:
- Test all required field validations
- Test email format validation
- Test phone number formats
- Test character limits
- Test special characters
- Test form submission
- Test error message clarity
- Provide detailed bug report
```

## Troubleshooting

### Agent Not Behaving as QA Tester
1. Check if `DEFAULT_QA_MODE=true` in your `.env`
2. Verify no override prompt is conflicting
3. Check the QA Settings tab configuration
4. Look at the logs for "Auto-injecting QA testing behavior"

### QA Prompts Not Working
1. Ensure `sys_prompt.txt` exists in the root directory
2. Check file permissions and encoding (UTF-8)
3. Verify the prompt is being loaded in the logs
4. Try setting the prompt directly in environment variables

### Agent Too Aggressive/Not Aggressive Enough
1. Adjust the "Testing Depth" slider in QA Settings
2. Modify the QA prompt in the settings
3. Use custom prompts for specific testing scenarios

## Advanced Configuration

### Custom QA Prompts
You can create specialized QA prompts for different types of testing:

```python
# In src/utils/qa_system_prompt.py
def get_security_testing_prompt():
    return """
    You are a security-focused QA tester.
    Focus on finding security vulnerabilities...
    """

def get_accessibility_testing_prompt():
    return """
    You are an accessibility QA tester.
    Focus on WCAG compliance and usability...
    """
```

### Integration with CI/CD
The QA agent can be integrated into automated testing pipelines:

```bash
# Run QA tests via API
curl -X POST http://localhost:7788/api/test \
  -H "Content-Type: application/json" \
  -d '{"task": "Test the checkout flow", "qa_mode": true}'
```

## Best Practices

1. **Start with Standard Testing Depth**: Level 3 provides good coverage without being excessive
2. **Use Extend Mode**: Don't override the complete system prompt unless necessary
3. **Provide Clear Tasks**: Specific testing objectives get better results
4. **Review QA Reports**: The agent generates detailed reports - use them!
5. **Iterate and Refine**: Adjust prompts based on the types of issues you want to catch

## Support

If you encounter issues:
1. Check the console logs for error messages
2. Verify your `.env` configuration
3. Test with a simple QA task first
4. Review the generated QA reports for insights
