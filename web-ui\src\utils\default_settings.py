"""
Default agent settings configuration.
These settings are used when the Agent Settings tab is hidden.
"""

import os

# Default LLM Configuration
DEFAULT_LLM_PROVIDER = os.getenv("DEFAULT_LLM", "google")  # "google" for Gemini
DEFAULT_LLM_MODEL = os.getenv("DEFAULT_LLM_MODEL", "gemini-2.0-flash")
DEFAULT_LLM_TEMPERATURE = float(os.getenv("DEFAULT_LLM_TEMPERATURE", "0.6"))
DEFAULT_USE_VISION = os.getenv("DEFAULT_USE_VISION", "true").lower() == "true"

# Default Agent Configuration
DEFAULT_MAX_STEPS = int(os.getenv("DEFAULT_MAX_STEPS", "100"))
DEFAULT_MAX_ACTIONS = int(os.getenv("DEFAULT_MAX_ACTIONS", "10"))
DEFAULT_MAX_INPUT_TOKENS = int(os.getenv("DEFAULT_MAX_INPUT_TOKENS", "128000"))
DEFAULT_TOOL_CALLING_METHOD = os.getenv("DEFAULT_TOOL_CALLING_METHOD", "auto")

# Default Planner LLM Configuration (optional)
DEFAULT_PLANNER_LLM_PROVIDER = os.getenv("DEFAULT_PLANNER_LLM_PROVIDER", "")  # Empty = no planner
DEFAULT_PLANNER_LLM_MODEL = os.getenv("DEFAULT_PLANNER_LLM_MODEL", "")
DEFAULT_PLANNER_LLM_TEMPERATURE = float(os.getenv("DEFAULT_PLANNER_LLM_TEMPERATURE", "0.6"))
DEFAULT_PLANNER_USE_VISION = os.getenv("DEFAULT_PLANNER_USE_VISION", "false").lower() == "true"

# Default System Prompts
DEFAULT_OVERRIDE_SYSTEM_PROMPT = os.getenv("DEFAULT_OVERRIDE_SYSTEM_PROMPT", "")

# Load QA testing prompt from sys_prompt.txt if available
def _load_qa_prompt():
    try:
        import os
        sys_prompt_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "sys_prompt.txt")
        if os.path.exists(sys_prompt_path):
            with open(sys_prompt_path, 'r', encoding='utf-8') as f:
                return f.read().strip()
    except Exception:
        pass
    return ""

DEFAULT_QA_PROMPT = _load_qa_prompt()

# QA Mode can be forced via environment variable
DEFAULT_QA_MODE = os.getenv("DEFAULT_QA_MODE", "true").lower() == "true"
DEFAULT_EXTEND_SYSTEM_PROMPT = os.getenv("DEFAULT_EXTEND_SYSTEM_PROMPT", DEFAULT_QA_PROMPT if DEFAULT_QA_MODE else "")

# Default Ollama Settings (if using Ollama)
DEFAULT_OLLAMA_NUM_CTX = int(os.getenv("DEFAULT_OLLAMA_NUM_CTX", "16000"))

# Default API Configuration
DEFAULT_LLM_BASE_URL = os.getenv("DEFAULT_LLM_BASE_URL", "")
DEFAULT_LLM_API_KEY = os.getenv("DEFAULT_LLM_API_KEY", "")  # Usually left empty to use .env
DEFAULT_PLANNER_LLM_BASE_URL = os.getenv("DEFAULT_PLANNER_LLM_BASE_URL", "")
DEFAULT_PLANNER_LLM_API_KEY = os.getenv("DEFAULT_PLANNER_LLM_API_KEY", "")

def get_default_agent_settings():
    """
    Get all default agent settings as a dictionary.
    """
    return {
        "llm_provider": DEFAULT_LLM_PROVIDER,
        "llm_model_name": DEFAULT_LLM_MODEL,
        "llm_temperature": DEFAULT_LLM_TEMPERATURE,
        "use_vision": DEFAULT_USE_VISION,
        "max_steps": DEFAULT_MAX_STEPS,
        "max_actions": DEFAULT_MAX_ACTIONS,
        "max_input_tokens": DEFAULT_MAX_INPUT_TOKENS,
        "tool_calling_method": DEFAULT_TOOL_CALLING_METHOD,
        "override_system_prompt": DEFAULT_OVERRIDE_SYSTEM_PROMPT,
        "extend_system_prompt": DEFAULT_EXTEND_SYSTEM_PROMPT,
        "ollama_num_ctx": DEFAULT_OLLAMA_NUM_CTX,
        "llm_base_url": DEFAULT_LLM_BASE_URL,
        "llm_api_key": DEFAULT_LLM_API_KEY,
        "planner_llm_provider": DEFAULT_PLANNER_LLM_PROVIDER,
        "planner_llm_model_name": DEFAULT_PLANNER_LLM_MODEL,
        "planner_llm_temperature": DEFAULT_PLANNER_LLM_TEMPERATURE,
        "planner_use_vision": DEFAULT_PLANNER_USE_VISION,
        "planner_llm_base_url": DEFAULT_PLANNER_LLM_BASE_URL,
        "planner_llm_api_key": DEFAULT_PLANNER_LLM_API_KEY,
    } 